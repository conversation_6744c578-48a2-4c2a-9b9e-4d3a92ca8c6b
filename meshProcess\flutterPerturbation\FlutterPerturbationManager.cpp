////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file FlutterPerturbationManager.cpp
//! <AUTHOR>
//! @brief 颤振摄动场管理器实现
//! @date 2024-01-01
//
//------------------------------修改日志----------------------------------------
// 2024-01-01 您的姓名
//     说明：实现颤振摄动场管理器
//
//------------------------------------------------------------------------------

#include "meshProcess/flutterPerturbation/FlutterPerturbationManager.h"

FlutterPerturbationManager::FlutterPerturbationManager(const Configure::Flow::FlowConfigure &configure_)
    : configure(configure_)
{
    // 从配置中读取颤振参数
    flutterFlag = configure.GetFlutter().flutterFlag;
    perturbationFieldNumber = configure.GetFlutter().perturbationFieldNumber;
    perturbationFieldPaths = configure.GetFlutter().perturbationFieldPaths;
    perturbationFieldNames = configure.GetFlutter().perturbationFieldNames;
    perturbationVariables = configure.GetFlutter().perturbationVariables;
    
    // 初始化变量映射
    scalarVariableMap["density"] = 0;
    scalarVariableMap["pressure"] = 1;
    scalarVariableMap["temperature"] = 2;
    
    vectorVariableMap["velocity"] = 0;
    vectorVariableMap["momentum"] = 1;
    
    if (flutterFlag && perturbationFieldNumber > 0)
    {
        Print("颤振摄动场管理器初始化完成，摄动场数量: " + ToString(perturbationFieldNumber));
    }
}

FlutterPerturbationManager::~FlutterPerturbationManager()
{
    // 析构函数实现
}

int FlutterPerturbationManager::ReadPerturbationFields(std::vector<SubMesh *> &globalMeshVector)
{
    if (!flutterFlag || perturbationFieldNumber == 0)
    {
        Print("未启用颤振计算或摄动场数量为0，跳过摄动场读取");
        return 0;
    }
    
    Print("开始读取颤振摄动场文件...");
    
    const int nZone = globalMeshVector.size();
    
    // 为每个区域创建摄动场数据结构
    for (int zoneID = 0; zoneID < nZone; zoneID++)
    {
        CreatePerturbationFieldStructure(globalMeshVector[zoneID]);
    }
    
    // 读取每个摄动场文件
    for (int perturbID = 0; perturbID < perturbationFieldNumber; perturbID++)
    {
        Print("读取摄动场 " + ToString(perturbID + 1) + "/" + ToString(perturbationFieldNumber));
        
        for (int zoneID = 0; zoneID < nZone; zoneID++)
        {
            std::string filePath = perturbationFieldPaths[perturbID];
            std::string fileName = perturbationFieldNames[perturbID];
            
            // 如果是多区域，文件名可能需要添加区域后缀
            if (nZone > 1)
            {
                fileName += "_zone" + ToString(zoneID);
            }
            
            int result = ReadSinglePerturbationField(filePath, fileName, perturbID, globalMeshVector[zoneID]);
            if (result != 0)
            {
                FatalError("读取摄动场文件失败: " + filePath + fileName);
                return result;
            }
        }
    }
    
    Print("颤振摄动场文件读取完成");
    return 0;
}

void FlutterPerturbationManager::CreatePerturbationFieldStructure(SubMesh *mesh)
{
    if (!mesh) return;
    
    const int nElem = mesh->GetElementSize();
    
    // 初始化摄动场容器
    mesh->vv_flutterPerturbationScalar.resize(perturbationFieldNumber);
    mesh->vv_flutterPerturbationVector.resize(perturbationFieldNumber);
    mesh->n_flutterPerturbationFields = perturbationFieldNumber;
    
    // 为每个摄动场创建变量场
    for (int perturbID = 0; perturbID < perturbationFieldNumber; perturbID++)
    {
        // 创建标量摄动场
        mesh->vv_flutterPerturbationScalar[perturbID].resize(scalarVariableMap.size());
        for (auto &pair : scalarVariableMap)
        {
            std::string fieldName = "flutter_" + pair.first + "_" + ToString(perturbID);
            mesh->vv_flutterPerturbationScalar[perturbID][pair.second] = 
                new ElementField<Scalar>(nElem, fieldName);
        }
        
        // 创建矢量摄动场
        mesh->vv_flutterPerturbationVector[perturbID].resize(vectorVariableMap.size());
        for (auto &pair : vectorVariableMap)
        {
            std::string fieldName = "flutter_" + pair.first + "_" + ToString(perturbID);
            mesh->vv_flutterPerturbationVector[perturbID][pair.second] = 
                new ElementField<Vector>(nElem, fieldName);
        }
    }
}

int FlutterPerturbationManager::ReadSinglePerturbationField(const std::string &filePath, 
                                                          const std::string &fileName,
                                                          const int &perturbationID, 
                                                          SubMesh *globalMesh)
{
    std::string fullPath = filePath + fileName;
    std::string fileFormat = ParseFileFormat(fileName);
    
    if (fileFormat == "tecplot")
    {
        // 读取Tecplot格式的摄动场文件
        // 这里需要根据实际的摄动场文件格式来实现
        Print("读取Tecplot格式摄动场文件: " + fullPath);
        // TODO: 实现Tecplot格式读取
    }
    else if (fileFormat == "cgns")
    {
        // 读取CGNS格式的摄动场文件
        Print("读取CGNS格式摄动场文件: " + fullPath);
        // TODO: 实现CGNS格式读取
    }
    else if (fileFormat == "binary")
    {
        // 读取二进制格式的摄动场文件
        Print("读取二进制格式摄动场文件: " + fullPath);
        
        std::fstream file(fullPath, std::ios::in | std::ios::binary);
        if (!file.is_open())
        {
            FatalError("无法打开摄动场文件: " + fullPath);
            return -1;
        }
        
        // 读取摄动场数据
        const int nElem = globalMesh->GetElementSize();
        
        // 读取标量摄动场
        for (auto &pair : scalarVariableMap)
        {
            ElementField<Scalar> *field = globalMesh->vv_flutterPerturbationScalar[perturbationID][pair.second];
            for (int elemID = 0; elemID < nElem; elemID++)
            {
                Scalar value;
                IO::Read(file, value, true);
                field->SetValue(elemID, value);
            }
        }
        
        // 读取矢量摄动场
        for (auto &pair : vectorVariableMap)
        {
            ElementField<Vector> *field = globalMesh->vv_flutterPerturbationVector[perturbationID][pair.second];
            for (int elemID = 0; elemID < nElem; elemID++)
            {
                Vector value;
                IO::Read(file, value, true);
                field->SetValue(elemID, value);
            }
        }
        
        file.close();
    }
    else
    {
        FatalError("不支持的摄动场文件格式: " + fileFormat);
        return -1;
    }
    
    return 0;
}

std::string FlutterPerturbationManager::ParseFileFormat(const std::string &fileName)
{
    size_t dotPos = fileName.find_last_of('.');
    if (dotPos == std::string::npos)
    {
        return "unknown";
    }
    
    std::string extension = fileName.substr(dotPos + 1);
    
    if (extension == "plt" || extension == "dat")
    {
        return "tecplot";
    }
    else if (extension == "cgns")
    {
        return "cgns";
    }
    else if (extension == "bin" || extension == "flutter")
    {
        return "binary";
    }
    else
    {
        return "unknown";
    }
}
