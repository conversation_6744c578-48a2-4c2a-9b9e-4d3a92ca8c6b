////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file FlutterPerturbationManager.h
//! <AUTHOR>
//! @brief 颤振摄动场管理器
//! @date 2024-01-01
//
//------------------------------修改日志----------------------------------------
// 2024-01-01 您的姓名
//     说明：建立颤振摄动场管理器
//
//------------------------------------------------------------------------------

#ifndef _meshProcess_flutterPerturbation_FlutterPerturbationManager_
#define _meshProcess_flutterPerturbation_FlutterPerturbationManager_

#include "basic/mesh/SubMesh.h"
#include "basic/field/ElementField.h"
#include "sourceFlow/configure/FlowConfigure.h"
#include "basic/common/IO.h"

/**
 * @brief 颤振摄动场管理器类
 * 
 * 负责读取、处理和分区颤振计算的摄动场数据
 */
class FlutterPerturbationManager
{
public:
    /**
     * @brief 构造函数
     * 
     * @param[in] configure_ 配置参数对象
     */
    FlutterPerturbationManager(const Configure::Flow::FlowConfigure &configure_);

    /**
     * @brief 析构函数
     */
    ~FlutterPerturbationManager();

    /**
     * @brief 读取摄动场文件
     * 
     * @param[in,out] globalMeshVector 全局网格容器
     * @return 成功返回0，失败返回错误码
     */
    int ReadPerturbationFields(std::vector<SubMesh *> &globalMeshVector);

    /**
     * @brief 为子网格分配摄动场数据
     * 
     * @param[in] globalMesh 全局网格
     * @param[out] subMesh 子网格
     * @param[in] decomposeIDList 分区ID列表
     */
    void AllocatePerturbationFieldsToSubMesh(SubMesh *globalMesh, SubMesh *subMesh, 
                                           const std::vector<int> &decomposeIDList);

    /**
     * @brief 写入摄动场数据到文件
     * 
     * @param[in] file 文件流
     * @param[in] subMesh 子网格
     * @param[in] binary 是否二进制格式
     */
    void WritePerturbationFields(std::fstream &file, const SubMesh *subMesh, const bool &binary);

    /**
     * @brief 从文件读取摄动场数据
     * 
     * @param[in] file 文件流
     * @param[out] subMesh 子网格
     * @param[in] binary 是否二进制格式
     */
    void ReadPerturbationFields(std::fstream &file, SubMesh *subMesh, const bool &binary);

    /**
     * @brief 获取摄动场数量
     * 
     * @return 摄动场数量
     */
    int GetPerturbationFieldNumber() const { return perturbationFieldNumber; }

    /**
     * @brief 判断是否启用颤振计算
     * 
     * @return 是否启用颤振计算
     */
    bool IsFlutterEnabled() const { return flutterFlag; }

private:
    /**
     * @brief 读取单个摄动场文件
     * 
     * @param[in] filePath 文件路径
     * @param[in] fileName 文件名
     * @param[in] perturbationID 摄动场ID
     * @param[in,out] globalMesh 全局网格
     * @return 成功返回0，失败返回错误码
     */
    int ReadSinglePerturbationField(const std::string &filePath, const std::string &fileName,
                                  const int &perturbationID, SubMesh *globalMesh);

    /**
     * @brief 创建摄动场数据结构
     * 
     * @param[in,out] mesh 网格对象
     */
    void CreatePerturbationFieldStructure(SubMesh *mesh);

    /**
     * @brief 解析摄动场文件格式
     * 
     * @param[in] fileName 文件名
     * @return 文件格式类型
     */
    std::string ParseFileFormat(const std::string &fileName);

private:
    const Configure::Flow::FlowConfigure &configure; ///< 配置参数
    bool flutterFlag; ///< 颤振计算标识
    int perturbationFieldNumber; ///< 摄动场数量
    std::vector<std::string> perturbationFieldPaths; ///< 摄动场文件路径
    std::vector<std::string> perturbationFieldNames; ///< 摄动场文件名
    std::vector<std::string> perturbationVariables; ///< 摄动变量名称
    
    // 摄动场变量映射
    std::map<std::string, int> scalarVariableMap; ///< 标量变量映射
    std::map<std::string, int> vectorVariableMap; ///< 矢量变量映射
};

#endif // _meshProcess_flutterPerturbation_FlutterPerturbationManager_
