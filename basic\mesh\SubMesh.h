﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file SubMesh.h
//! <AUTHOR>
//! @brief 基本网格类（分区后）
//! @date 2020-07-27
//
//------------------------------修改日志----------------------------------------
// 2021-12-31 李艳亮、乔龙（气动院）
//    说明：调整并规范化。
//
// 2020-07-27 凌空
//    说明：建立。
//------------------------------------------------------------------------------
#ifndef _basic_mesh_SubMesh_
#define _basic_mesh_SubMesh_

#include "basic/mesh/MultiGrid.h"

// 用于友元类的声明（暂时）
class DecomposeManager;
class DualMesh;
class MeshProcess;
class DlgMesh;

/**
 * @class SubMesh
 * @brief 子网格类，继承自Mesh类，用于处理分布式计算中的局部网格数据
 * 
 * 该类管理局部网格与全局网格之间的映射关系，支持多重网格计算，
 * 并提供边界处理、虚拟单元创建等功能。
 */
class SubMesh : public Mesh
{
public:
    /**
     * @brief 默认构造函数
     * @param[in] partID 分区ID，默认为0
     * @param[in] nLevel 多重网格层数，默认为0
     */
    SubMesh(const int &partID = 0, const int &nLevel = 0);
    
    /**
     * @brief 从文件构造子网格
     * @param[in] fileName 网格文件名
     * @param[in] nLevel 多重网格层数
     * @param[in] binary 是否为二进制文件
     */
    SubMesh(const std::string &fileName, const int &nLevel, const bool &binary);
    
public: // 全局ID获取接口
    /**
     * @brief 获取单元全局ID
     * @param[in] localID 局部单元ID
     * @return 全局单元ID的常量引用
     */
    const int &GetElementGlobalID(const int &localID) const;
    
    /**
     * @brief 获取面全局ID
     * @param[in] localID 局部面ID
     * @return 全局面ID的常量引用
     */
    const int &GetFaceGlobalID(const int &localID) const;
    
    /**
     * @brief 获取节点全局ID
     * @param[in] localID 局部节点ID
     * @return 全局节点ID的常量引用
     */
    const int &GetNodeGlobalID(const int &localID) const;
    
    /**
     * @brief 获取边界面的全局索引
     * @param[in] localPatchID 局部边界块ID
     * @param[in] localIndex 局部边界索引
     * @return 全局边界索引的常量引用
     */
    const int &GetBoundaryFaceGlobalIndex(const int &localPatchID, const int &localIndex) const;
    
    /**
     * @brief 获取边界的全局ID
     * @param[in] level 网格层数
     * @param[in] localPatchID 局部边界块ID
     * @return 全局边界ID的常量引用
     */
    const int &GetBoundaryGlobalID(const int &level, const int &localPatchID) const;

    /**
     * @brief 获取指定层数的多重网格
     * @param[in] level 网格层数
     * @return 指向MultiGrid对象的指针
     */
    MultiGrid* GetMultiGrid(const int &level);
    
    /**
     * @brief 获取总网格层数
     * @return 总网格层数的常量引用
     */
    const int &GetTotalLevel() const { return n_level; }    
    
    /**
     * @brief 从文件读取子网格数据
     * @param[in] file 文件流对象
     * @param[in] nLevel 多重网格层数
     * @param[in] binary 是否为二进制文件
     */
    void ReadSubMesh(std::fstream &file, const int &nLevel, const bool &binary);

    /**
     * @brief 清空网格数据
     */
    void ClearMesh();

    /**
     * @brief 预计算网格数据
     * @param[in] dualFlag 是否使用对偶网格标志
     * @param[in] symmetryPatchIDList 对称边界块ID列表
     */
    void PreCalculate(const bool &dualFlag, const std::vector<std::vector<int>> &symmetryPatchIDList);
    
    /**
     * @brief 创建虚拟单元
     * @param[in] fineLevel 细网格层数
     * @details 用于处理并行计算中的边界数据交换，创建虚拟单元以保持计算一致性
     */
    void CreateGhostElement(const int &fineLevel);

private:
    /**
     * @brief 将子网格数据写入文件
     * @param[in] file 文件流对象
     * @param[in] nLevel 多重网格层数
     * @param[in] binary 是否为二进制文件，默认为true
     */
    void WriteSubMesh(std::fstream &file, const int &nLevel, const bool &binary = true);

protected:
    int n_pros;                             ///< 网格所在进程号
    int n_level;                            ///< 多重网格总层数（含细网格）
    std::vector<int> v_nodeID_Global;       ///< 局部网格与全局网格的点编号对应关系    
    std::vector<int> v_faceID_Global;       ///< 局部网格与全局网格的面编号对应关系    
    std::vector<int> v_elemID_Global;       ///< 局部网格与全局网格的单元编号对应关系   
    std::vector<std::vector<int>> vv_boundaryFaceIndex_Global;  ///< 局部网格与全局网格的边界面索引对应关系
    std::vector<std::vector<int>> v_boundaryID_Global;  ///< 不同level局部网格与全局网格的边界编号对应关系
    std::vector<MultiGrid> v_multiGrid;     ///< 存放粗网格的容器

    // 颤振摄动场数据
    std::vector<std::vector<ElementField<Scalar>*>> vv_flutterPerturbationScalar; ///< 标量摄动场容器[摄动场ID][变量ID]
    std::vector<std::vector<ElementField<Vector>*>> vv_flutterPerturbationVector; ///< 矢量摄动场容器[摄动场ID][变量ID]
    int n_flutterPerturbationFields; ///< 摄动场数量

#if defined(_BaseParallelMPI_)
public:
    /**
     * @brief 序列化函数
     * @tparam Archive 序列化存档类型
     * @param[in] ar 序列化存档对象
     * @param[in] version 版本号
     */
    template<class Archive>
    void serialize(Archive& ar, const unsigned int version)
    {
        // 基类成员序列化
        ar & boost::serialization::base_object<Mesh>(*this);
        
        // 当前类成员序列化
        ar & n_pros;
        ar & n_level;
        ar & v_nodeID_Global;
        ar & v_faceID_Global;
        ar & v_elemID_Global;
        ar & vv_boundaryFaceIndex_Global;
        ar & v_boundaryID_Global;
        ar & v_multiGrid;
    }
#endif

    // 友元类声明
    friend class DecomposeManager;  ///< 网格分解管理器
    friend class DualMesh;          ///< 对偶网格类
    friend class MeshProcess;       ///< 网格处理类
    friend class DlgMesh;           ///< 对话框网格类
};

#endif // _basic_mesh_SubMesh_
