////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file FlowConfigure.h
//! <AUTHOR>
//! @brief 读xml文件的类。包含用户交接界面与生成boundary.xml文件的函数。
//! @date 2021-03-30
//
//------------------------------修改日志----------------------------------------
// 2021-04-09 吴奥奇
//    说明：修改GenerateBoundaryFile函数
//            添加材料参数
//            添加获得湍流参数的俩函数
//            添加获得边界条件类型的函数
//
// 2021-04-07 吴奥奇
//    说明：由于xml文件内容改变，重写Read函数
//
// 2021-03-30 吴奥奇
//    说明：代码规范化，减少pushback
//            添加命名空间
//------------------------------------------------------------------------------

#ifndef _sourceFlow_configure_FlowConfigure_
#define _sourceFlow_configure_FlowConfigure_

#include "basic/configure/Configure.h"
#include "sourceFlow/configure/FlowConfigureMacro.h"
#include "sourceFlow/configure/FlowConfigureMacro.hxx"
#include "sourceFlow/configure/FlowGlobalDataMacro.h"

/**
 * @brief 参数命名空间
 *
 */
namespace Configure
{
/**
 * @brief 流场参数命名空间
 *
 */
namespace Flow
{
/**
 * @brief 材料参数结构体
 *
 */
struct MaterialStruct
{
    Material::Flow::DensityType density; ///< 密度类型
    Material::Flow::ViscosityType viscosity; ///< 粘性类型
	Material::Flow::CpType cpType; ///< cp计算方法
    MaterialStruct()
    {
        density = Material::Flow::DensityType::IDEAL_GAS;
        viscosity = Material::Flow::ViscosityType::SUTHERLAND;
		cpType = Material::Flow::CpType::cptConstant;
    }
};

/**
 * @brief 远场参数结构体
 *
 */
struct FlowReferenceStruct
{
    ReferenceType type;              ///< 参考量设置类型
    Scalar R;                        ///< 来流气体常数
    Scalar gamma;                    ///< 来流比热比
    Scalar alpha;                    ///< 来流攻角
    Scalar beta;                     ///< 来流侧滑角
    Scalar mach;                     ///< 来流马赫数
    Scalar staticTemperature;        ///< 来流静温
    Scalar totalTemperature;         ///< 来流总温
    Scalar staticPressure;           ///< 来流静压
    Scalar totalPressure;            ///< 来流总压
    Scalar density;                  ///< 来流密度
    Vector velocity;                 ///< 来流速度
    Scalar sound;                    ///< 来流声速
    Scalar Reynolds;                 ///< 来流雷诺数
    Scalar turbulentIntensity;       ///< 来流湍流强度
    Scalar turbulentViscosityRatio;  ///< 来流湍流粘性比
    Scalar muLaminar;                ///< 来流层流粘性系数
	Scalar altitude;                 ///< 高度
    FlowReferenceStruct()
    {
        type = ReferenceType::MACH_REYNOLDS;
        gamma = 1.4;
        alpha = 0.0;
        beta = 0.0;
        mach = 0.0;
        staticTemperature = 288.15;
        totalTemperature = 288.15;
        staticPressure = 101325.0;
        totalPressure = 101325.0;
        density = 1.225;
		velocity = Vector0;
		sound = 340.29;
        Reynolds = 0.0;
        turbulentIntensity = 0.0;
        turbulentViscosityRatio = 0.0;
        muLaminar = 1.78938e-005;
		altitude = 0.0;
    }
};

/**
 * @brief 多组分参数结构体
 *
 */
struct MultiSpeciesStruct
{
	std::vector<Scalar> massFractionR;
	std::vector<Scalar> massFractionP;
	std::vector<std::string> reactants;
	std::vector<std::string> productions;
	Scalar ignitionLength;
	Scalar ignitionTemperature;
    MultiSpeciesStruct()
    {
	    ignitionLength = Scalar0;
	    ignitionTemperature = Scalar0;
    }
};

/**
 * @brief 物理模型参数结构体
 *
 */
struct ModelStruct
{
    Turbulence::Model type; ///< 模型类型
    Turbulence::WallFunction wallFunctionMethod; ///< 壁面函数计算方法
    bool crossDiffusionFlag; ///< 湍流交叉项采用扩散项离散方式计算标识
    Scalar PrandtlLaminar; ///< 层流普朗特数
    Scalar PrandtlTurbulent; ///< 湍流普朗特数
    Scalar DESconstant; ///< DES尺度常数
    Scalar Cdt; ///< 延迟函数参数
    bool filterVolume; ///< 使用体积三次方根作为DES尺度
	bool multiSpeciesFlag; ///< 多组分计算标识
	bool chemicalModelFlag; ///< 化学反应计算标识
    MultiSpeciesStruct multiSpecies; ///< 多组分控制参数
    std::string modelPath; ///< 机器学习模型路径
    int IntraOpNumThreads; ///< 机器学习模型算子内，推理并行核数
    int InterOpNumThreads; ///< 机器学习模型算子间，推理并行核数
    int predictInterval; ///< 机器学习模型算子间，推理并行核数
    bool trainFlag; ///< 生成训练集标识
    bool updateTurbOnly; ///只更新湍流
    ModelStruct()
    {
        type = Turbulence::Model::INVISCID;
        wallFunctionMethod = Turbulence::WallFunction::NONE_WALL_FUNCTION;
        crossDiffusionFlag = true;
        PrandtlLaminar = 0.72;
        PrandtlTurbulent = 0.9;
		DESconstant = 0.65;
		Cdt = 8.0;
        filterVolume = false;
		multiSpeciesFlag = false;
		chemicalModelFlag = false;
        modelPath = "./ml_model.onnx";
        IntraOpNumThreads = 4;
        InterOpNumThreads = 1;
        predictInterval = 10000;
        trainFlag = false;
        updateTurbOnly = false;
    }
};

/**
 * @brief 空间离散格式结构体
 *
 */
struct FluxSchemeStruct
{
    Flux::ReconstructionOrder reconstructOrder; ///< 空间重构阶数
    Flux::ReconstructionOrder turbulenceOrder; ///< 湍流重构阶数
    FieldManipulation::GradientScheme gradient; ///< 梯度计算方法
    Flux::Flow::Limiter::Scheme  limiter; ///< 限制器类型
    Flux::Flow::Inviscid::Scheme inviscid; ///< 对流项计算格式
    Flux::Flow::Viscous::Scheme viscous; ///< 粘性项计算格式
    Flux::Flow::Source::Scheme source; ///< 源项计算方法
    FluxSchemeStruct()
    {
        reconstructOrder = Flux::ReconstructionOrder::SECOND;
        turbulenceOrder = Flux::ReconstructionOrder::SECOND;
        limiter = Flux::Flow::Limiter::VENKATAKRISHNAN;
        gradient = FieldManipulation::GradientScheme::GREEN_GAUSS;
        inviscid = Flux::Flow::Inviscid::CENTRAL;
        viscous = Flux::Flow::Viscous::CENTRAL_DISTANCE;
        source = Flux::Flow::Source::NONE_SOURCE;
    }
};

/**
 * @brief Runge-Kutta参数结构体
 *
 */
struct RungeKuttaStruct
{
    int stages; ///< Runge-Kutta阶数
    Time::RungeKuttaType type; ///< Runge-Kutta粘性通量计算类型
    std::vector<Scalar> coefficients; ///< Runge-Kutta系数
    RungeKuttaStruct()
    {
        stages = 3;
        type = Time::RungeKuttaType::HYBRID;
        coefficients = std::vector<Scalar>{0.66667, 0.66667, 1.0};
    }
};

/**
 * @brief LUSGS参数结构体
 *
 */
struct LUSGSStruct
{
    Scalar underRelax; ///< 松弛系数
    LUSGSStruct()
    {
        underRelax = 2.0;
    }
};

struct ExactJacobianStruct
{
    Time::LinearSolverType solverType;
    Time::LinearSolverPreconditionerType preconditionerType;
    Scalar linearError;
    int maxIterStep;
    bool restartFlag;
    int restartStep;
    ExactJacobianStruct()
    {
        //设定默认值
#if defined(_EnablePETSC_)
        solverType = Time::LinearSolverType::PETSC;
#else
        solverType = Time::LinearSolverType::PRIVATE;
#endif
        preconditionerType = Time::LinearSolverPreconditionerType::JACOBI;
        linearError = 0.01;
        maxIterStep = 30;
        restartFlag = false;
        restartStep = 10;
    }
};

/**
 * @brief CFL数参数结构体
 *
 */
struct CFLStruct
{
    Scalar value; ///< 细网格CFL数
    Scalar coarseMeshRatio; ///< 粗网格CFL变化率
    int variableFlag; ///< CFL变化标识
    Scalar max; ///< 最大CFL数
    Scalar min; ///< 最小CFL数
    Scalar growthRatio; ///< CFL数变化率
    int growthStep; ///< CFL数变化步数
    CFLStruct()
    {
        value = 1.25;
        coarseMeshRatio = 0.8;
        variableFlag = 0;
        max = 10000.0;
        min = 1.0;
        growthRatio = 1.02;
        growthStep = 500;
    }
};

/**
 * @brief 时间离散参数结构体
 *
 */
struct TimeSchemeStruct
{
    Time::UnsteadyType outerLoopType; ///< 非定常类型
    Time::Scheme innnerLoopType; ///< 时间推进方法
    RungeKuttaStruct RungeKutta; ///< Runge-Kutta参数结构体
    LUSGSStruct LUSGS; ///< LUSGS参数结构体
    ExactJacobianStruct exactJacobian;
    CFLStruct CFL; ///< CFL数结构体
    Time::UnsteadyOrder unsteadyTimeOrder; ///< 非定常计算时间离散阶数
    TimeSchemeStruct()
    {
        outerLoopType = Time::UnsteadyType::STEADY;
        innnerLoopType = Time::Scheme::RUNGE_KUTTA;
        unsteadyTimeOrder = Time::UnsteadyOrder::SECOND;
    }
};

/**
 * @brief 多重网格求解参数结构体
 *
 */
struct MultigridSolverStruct
{
    int level; ///< 多重网格求解总层数
    MultigridType::Type type; ///< 多重网格循环类型
    MultigridType::TransferOperator restrictionOperator; ///< 细网格向粗网格插值方法
    MultigridType::TransferOperator prolongationOperator; ///< 粗网格向细网格延拓方法
    bool coarseMeshTurbulenceFlag; ///< 粗网格上是否求解湍流方程，true为求解
    MultigridSolverStruct()
    {
        level = 3;
        type = MultigridType::Type::W;
        restrictionOperator = MultigridType::TransferOperator::INJECTION;
        prolongationOperator = MultigridType::TransferOperator::INJECTION;
        coarseMeshTurbulenceFlag = true;
    }
};

/**
 * @brief 加速收敛技术参数结构体
 *
 */
struct AccelerationStruct
{
    MultigridSolverStruct multigridSolver; ///< 多重网格求解参数结构体
    Smoother::Scheme residualSmooth; ///< 残值光顺方法
    bool preconditionFlag; ///< 低速预处理标识
    bool entropyFixFlag; ///<熵修正标识
    AccelerationStruct()
    {
        residualSmooth = Smoother::Scheme::DISTANCE_WEIGHT;
        preconditionFlag = false;
        entropyFixFlag = true;
    }
};

/// 矩形、长方体
struct RectangleStruct { Vector min, max; };

/// 圆、球
struct SphereStruct { Vector center; Scalar radius; };

/// 补丁结构体
struct PatchStruct
{
	std::string shape; ///< 几何形状
	RectangleStruct *rectangle; ///< 矩形、长方体
	SphereStruct *sphere; ///< 圆、球
	int variableNumber; ///< 变量数量
	std::vector<std::pair<std::string, std::string>> variables; ///< 变量名称和取值
};

/**
 * @brief 初始化参数结构体
 *
 */
struct InitializationStruct
{
    Initialization::Type type; ///< 初始化方法
    int restartStep; ///< 续算起始步
    std::string initialFilePath; ///< 文件处理化路径
    int fullMultigridSteps; ///< 全多重初始化循环步数
    Scalar fullMultigridCriteria; ///< 全多重收敛标准
	int patchNumber; ///< 初始化补丁数量
	std::vector<PatchStruct> patches; ///< 初始化补丁信息
    InitializationStruct()
    {
        type = Initialization::Type::REFERENCE;
        restartStep = 0;
        initialFilePath = "./0/";
        fullMultigridSteps = 0;
        fullMultigridCriteria = 1.0E-5;
		patchNumber = 0;
    }
};

/**
 * @brief 外循环参数结构体
 *
 */
struct OuterLoopStruct
{
    int steps; ///< 迭代步数
    int interval; ///< 结果保存间隔步数
    int averagedStep; ///< 这步开始进行时均流场计算
    Scalar timeStep; ///< 物理时间步长
    Scalar totalTime; ///< 总物理时间
    bool resetAverage; ///< 重新计算时均流场标识
    OuterLoopStruct()
    {
        steps = 1;
        interval = 0;
		averagedStep = 0;
        timeStep = 1.0;
        totalTime = 1.0;
		resetAverage = false;
    }
};

/**
 * @brief 内循环参数结构体
 *
 */
struct InnerLoopStruct
{
    int steps; ///< 迭代步数
    int interval; ///< 保存间隔
    int monitorInterval; ///< 监控间隔
    Scalar criteria; ///< 收敛标准
    InnerLoopStruct()
    {
        steps = 1000;
        interval = 0;
        monitorInterval = 10;
        criteria = 1.0E-6;
    }
};

/**
 * @brief 控制参数结构体
 *
 */
struct ControlStruct
{
    InitializationStruct initialization; ///< 初始化参数结构体
    OuterLoopStruct outerLoop; ///< 外循环参数结构体
    InnerLoopStruct innerLoop; ///< 内循环参数结构体
    std::string resultSavePath; ///< 计算结果保存路径
    ControlStruct()
    {
        resultSavePath = "./results/";
    }
};

/**
 * @brief MRF参数结构体
 *
 */
struct MRFStruct
{
	bool MRFFlag;
	std::string MRFName;
	Vector MRForigin;
	Vector MRFaxis;
	Scalar MRFomega;
};

/**
 * @brief 监控残值控制参数结构体
 *
 */
struct MonitorResidualsStruct
{
    bool massFlag; ///< 质量残值监控标识
    bool momentumFlag; ///< 动量残值监控标识
    bool energyFlag; ///< 能量残值监控标识
    bool turbulenceFlag; ///< 湍流量残值监控标识
    bool maxMassFlag; ///< 质量残值最大值监控标识
    bool maxTurbulenceFlag; ///< 湍流残值最大值监控标识
    bool log10Flag; ///< 监控残值取对数标识
    bool normalizedFlag; ///< 监控残值归一化标识
    MonitorResidualsStruct()
    {
        massFlag = true;
        momentumFlag = false;
        energyFlag = false;
        turbulenceFlag = true;
        maxMassFlag = false;
        maxTurbulenceFlag = false;
        log10Flag = true;
        normalizedFlag = true;
    }
};

/**
 * @brief 监控力系数控制参数结构体
 *
 */
struct MonitorForcesStruct
{
    bool ClFlag; ///< 升力系数监控标识
    bool CdFlag; ///< 阻力系数监控标识
    bool CmFlag; ///< 力矩系数监控标识
	std::vector<int> boundaryIDList; ///< 需要累加的边界ID
    MonitorForcesStruct()
    {
        ClFlag = true;
        CdFlag = true;
        CmFlag = false;
		boundaryIDList = std::vector<int>();
    }
};

/**
 * @brief 监控参数结构体
 *
 */
struct MonitorStruct
{
    MonitorResidualsStruct residuals; ///< 监控残值参数结构体
    MonitorForcesStruct forces; ///< 监控力系数参数结构体
};

/**
* @brief 重叠网格参数结构体
*
*/
struct OversetStruct
{
	//OversetType::InterpolationType interpolationType = OversetType::InverseDistance;
	OversetType::InterpolationType interpolationType;
	bool enableOverset; ///< 是否启用重叠网格
	OversetStruct():interpolationType(OversetType::InverseDistance), enableOverset(false){}
};

/**
* @brief 平移+旋转运动参数结构体
*
*/
struct MotionTranslationRotationStruct
{
//	Vector velocity = {0, 0, 0 };    //m/s
//	Vector axisOrigin = {0, 0, 0};  // m
//	Vector axisDirection = { 1, 0, 0 };
//	Scalar rotationRate = 0;  // rad/s
	Vector velocity;    //m/s
    Vector axisOrigin;  // m
    Vector axisDirection;
    Scalar rotationRate;;  // rad/s
	MotionTranslationRotationStruct():velocity(0,0,0),axisOrigin(0,0,0),axisDirection(1,0,0),rotationRate(0){}
};

/**
* @brief XDOF运动参数结构体
*
*/
struct MotionXdofStruct
{
	Scalar mass;  // kg
	Scalar releaseTime;  // s
	Vector CenterOfMass;  // m
    Vector degree;//初始欧拉角°
	Scalar MomentOfInertia[6]; // Reference to the center of mass, kg-m^2
	Vector initialVelocity; // m/s
};

/**
* @brief 变形运动参数结构体
*
*/
struct MotionMorphingStruct
{

};


/**
* @brief 运动参数结构体
*
*/
struct MotionStruct
{
	MotionType::Type motionType; ///< 运动类型
	MotionTranslationRotationStruct translationRotation; ///< 平移+旋转运动参数结构体
	MotionXdofStruct xdof;  ///< XDOF运动参数结构体
	MotionMorphingStruct morphing;
};

/**
* @brief 后处理参考值结构体
*
*/
struct ForceNondimensionalizedStruct
{
	Vector lengthRef; ///< 参考长度
	Vector SRef; ///< 参考面积
	Vector momentCenter; ///< 力矩中心

	Scalar alpha; ///< 攻角
	Scalar beta; ///< 侧滑角
	Scalar pressure; ///< 参考压力
	Scalar density; ///< 参考密度
	Scalar velocityMag; ///< 参考速度

	std::vector<int> boundaryIDList; ///< 需要累加的边界ID

	ForceNondimensionalizedStruct()
	{
		lengthRef = Vector(1.0, 1.0, 1.0);
		SRef = Vector(1.0, 1.0 ,1.0);
		momentCenter = Vector0;

		alpha = 0.0;
		beta = 0.0;
		pressure = 101325.0;
		density = 1.225;
		velocityMag = 272.2352;
		boundaryIDList = std::vector<int>();

	}
};

/**
 * @brief 后处理参数结构体
 *
 */
struct PostprocessStruct
{

    Post::Type resultType; ///< 后处理结果输出类型
	Post::Position resultPosition; ///< 后处理物理量输出位置
	bool exportInteriorFlag; ///< 输出内部区域标识
	std::vector<std::string> exportValueName; ///< 输出变量名称，空白为全部输出
    bool exportFeaturesFlag;
    bool exportMutFlag;
    PostprocessStruct()
    {
        resultType = Post::Type::TECPLOT;
        resultPosition = Post::Position::CELL_CENTER;
		exportInteriorFlag = false;
		exportValueName = {};
        exportFeaturesFlag = false;
        exportMutFlag = false;
    }
};


/**
 * @brief 气弹结构体
 *
 */
struct CFDParameterStruct
{
    int meshNumber;
    std::vector<std::string> meshPath;            ///< 初始网格路径
    std::vector<std::string> fileName;            ///< 初始网格文件名称
    std::vector<Preprocessor::MeshType> meshType; ///< 网格类型

    MeshTransformStruct meshTransform; ///< 网格变换参数结构体

    std::string DeformMeshFile; // 变形网格名称
    Vector scale;
    Scalar Radiu;
    bool ReMesh_Flag;

    CFDParameterStruct()
    {
        meshNumber = 1;
        meshPath.clear();
        fileName.clear();
        meshType.clear();
        scale = Vector(1, 1, 1);
        Radiu = 1.0;
        ReMesh_Flag = true;
    }
};

struct CSDParameterStruct
{
    std::string MeshName;
    Vector scale;
    double ForceScale;
    Scalar Radiu;
    std::string NastranPath;
    std::string RwingDir;
    std::string FuseDir;
    Vector FEMtransfer;

    CSDParameterStruct()
    {
        scale = Vector(1, 1, 1);
        Radiu = 1.0;
        FEMtransfer = Vector(0.0, 0.0, 0.0);
        ForceScale = 1.0;
        RwingDir = 'X';
        FuseDir = 'Y';
    }
};


struct StaticAeroStruct
{
    bool staticAEFlag;
    CSDParameterStruct CSDParameter; ///< 气弹结构体
	CFDParameterStruct CFDParameter;
    int FSI_ITMAX;
    double relaxFactor;
	double Delta_Deform;
	bool Output_Flag;
	bool OneStepSolver_Flag;
	StaticAeroStruct():Output_Flag(true), OneStepSolver_Flag(false), relaxFactor(1.0), staticAEFlag(false) {}
};

/**
 * @brief 颤振计算参数结构体
 */
struct FlutterStruct
{
    bool flutterFlag; ///< 是否启用颤振计算
    int perturbationFieldNumber; ///< 摄动场数量
    std::vector<std::string> perturbationFieldPaths; ///< 摄动场文件路径
    std::vector<std::string> perturbationFieldNames; ///< 摄动场文件名
    std::vector<std::string> perturbationVariables; ///< 摄动变量名称列表

    FlutterStruct()
    {
        flutterFlag = false;
        perturbationFieldNumber = 0;
    }
};
/**
 * @brief 流场控制参数类
 *
 */
class FlowConfigure : public Configure
{
public:
    /**
     * @brief 构造函数
     *
     */
    FlowConfigure();

    /**
     * @brief 析构函数
     *
     */
    ~FlowConfigure();

    /**
     * @brief 从xml格式参数文件读取控制参数
     *
     * @param[in] fileName 参数文件名称
     */
    void ReadCaseXml(const std::string &fileName);

    /**
     * @brief 从xml格式参数文件读取气弹控制参数
     *
     * @param[in] fileName 参数文件名称
     */
    void ReadAeroStatic(PropertyTree &ptree);

    /**
     * @brief 获取材料参数
     *
     * @return const MaterialStruct&
     */
    const MaterialStruct &GetMaterial()const {return material;}

    /**
     * @brief 获取流动参考量参数
     *
     * @return const FlowReferenceStruct&
     */
    const FlowReferenceStruct &GetFlowReference()const {return reference;}

    /**
     * @brief 获取模型参数
     *
     * @return const ModelStruct&
     */
    const ModelStruct &GetModel()const {return model;}

    /**
     * @brief 获取空间离散格式参数
     *
     * @param level 当前网格层级
     * @return const FluxSchemeStruct&
     */
    const FluxSchemeStruct &GetFluxScheme(const int &level) const {return fluxScheme[Min(level,1)];}

    /**
     * @brief 获取时间离散格式参数
     *
     * @return const TimeSchemeStruct&
     */
    const TimeSchemeStruct &GetTimeScheme()const {return timeScheme;}

    /**
     * @brief 获取加速收敛方法参数
     *
     * @return const AccelerationStruct&
     */
    const AccelerationStruct &GetAcceleration()const {return acceleration;}


    /**
     * @brief 获取气弹参数
     *
     * @return const AccelerationStruct&
     */
    const StaticAeroStruct &GetStaticAero()const {return staticaero;}

    /**
     * @brief 获取控制参数
     *
     * @return const ControlStruct&
     */
    const ControlStruct &GetControl() const {return control;}

    /**
     * @brief 获取控制参数
     *
     * @return const ControlStruct&
     */
    const MRFStruct &GetMRF() const { return mrf; }

    /**
     * @brief 获取监控参数
     *
     * @return const MonitorStruct&
     */
    const MonitorStruct &GetMonitor() const {return monitor;}

    /**
     * @brief 获取后处理参数
     *
     * @return const PostprocessStruct&
     */
	const PostprocessStruct &GetPostprocess() const { return postprocess; }

	/**
	* @brief 获取无量纲化力与力矩参考量参数
	*
	* @return const PostprocessStruct&
	*/
	const ForceNondimensionalizedStruct &GetForceNondimensionalized() const { return forceNondimensionalized; }

	/**
	* @brief 获取重叠网格参数
	*
	* @return const OversetStruct&
	*/
	const OversetStruct &GetOverset() const { return overset; }

	/**
	* @brief 获取运动参数
	*
	* @return const MotionStruct&
	*/
	const std::vector<MotionStruct> &GetMotion() const { return motion; }

    /**
     * @brief 设置计算结果保存路径
     *
     * @return const std::string&
     */
    void SetResultsPath(const std::string &path_) { control.resultSavePath = path_; }

	/**
	* @brief 输出流场计算参数
	*
     * @param fileName_ 文件名称
	*/
	void WriteFile(const std::string fileName_ = "");

    /**
    * @brief 打印参考值信息
    *
    */
    void PrintReferenceValues();

private:
    /**
     * @brief 读取材料参数
     *
     * @param ptree 由参数文件生成的树
     */
    void ReadMaterial(PropertyTree &ptree);

    /**
     * @brief 读取流动参考量参数
     *
     * @param ptree 由参数文件生成的树
     */
    void ReadFlowReference(PropertyTree &ptree);

    /**
     * @brief 读取模型参数
     *
     * @param ptree 由参数文件生成的树
     */
    void ReadModel(PropertyTree &ptree);

    /**
     * @brief 读取空间离散格式参数
     *
     * @param ptree 由参数文件生成的树
     */
    void ReadFluxScheme(PropertyTree &ptree);

    /**
     * @brief 读取时间离散参数
     *
     * @param ptree 由参数文件生成的树
     */
    void ReadTimeScheme(PropertyTree &ptree);

    /**
     * @brief 读取加速收敛方法参数
     *
     * @param ptree 由参数文件生成的树
     */
    void ReadAcceleration(PropertyTree &ptree);

    /**
     * @brief 读取控制参数
     *
     * @param ptree 由参数文件生成的树
     */
    void ReadControl(PropertyTree &ptree);

    /**
     * @brief 获取MRF参数
     *
     * @param ptree 由参数文件生成的树
     */
    void ReadMRF(PropertyTree &ptree);

    /**
     * @brief 读取监控参数
     *
     * @param ptree 由参数文件生成的树
     */
    void ReadMonitor(PropertyTree &ptree);

    /**
     * @brief 读取后处理参数
     *
     * @param ptree 由参数文件生成的树
     */
    void ReadPostprocess(PropertyTree &ptree);

	/**
	* @brief 读取无量纲化力与力矩参考量参数
	*
	* @param ptree 由参数文件生成的树
	*/
	void ReadForceNondimensionalized(PropertyTree &ptree);

	/**
	* @brief 读取重叠网格参数
	*
	* @param ptree 由参数文件生成的树
	*/
	void ReadOverset(PropertyTree &ptree);

	/**
	* @brief 读取运动参数
	*
	* @param ptree 由参数文件生成的树
	*/
	void ReadMotion(PropertyTree &ptree);

    /**
     * @brief 根据材料参数更新远场参数
     *
     */
    void UpdateFlowReference();

    /**
     * @brief 检查参数是否合理
     *
     * @note 源项通量格式必须为NONE
     * @note 网格为2维时侧滑角为0
     * @note 湍流模型为无粘时，粘性项通量格式为NONE
     * @note 湍流模型不是无粘时，粘性项通量格式不能为NONE
     * @note 多重网格层数为2时，多重网格格式为V
     */
    void InspectConfigure();

private:
    MaterialStruct material; ///< 材料参数结构体
    FlowReferenceStruct reference; ///< 流动参考量参数结构体
    ModelStruct model; ///< 模型参数结构体
    std::vector<FluxSchemeStruct> fluxScheme; ///< 空间离散参数结构体
    TimeSchemeStruct timeScheme; ///< 时间离散参数结构体
    AccelerationStruct acceleration; ///< 加速收敛技术参数结构体
    ControlStruct control; ///< 控制参数结构体
    MRFStruct mrf;///< MRF参数结构体
    MonitorStruct monitor; ///< 监控参数结构体
    PostprocessStruct postprocess; ///< 后处理参数结构体
	ForceNondimensionalizedStruct forceNondimensionalized; ///< 无量纲化力与力矩参考量参数结构体
    OversetStruct overset; ///< 重叠网格参数结构体
    std::vector<MotionStruct> motion; ///< 运动参数结构体
    StaticAeroStruct staticaero;//<气弹结构体
};

} // namespace Flow
} // namespace Configure
#endif
