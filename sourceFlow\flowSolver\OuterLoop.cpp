﻿#include "sourceFlow/flowSolver/OuterLoop.h"

OuterLoop::OuterLoop(SubMesh *subMesh_, Configure::Flow::FlowConfigure &flowConfigure_)
	: subMesh(subMesh_), flowConfigure(flowConfigure_)
{
	// 创建物理场包容器
	for (int level = 0; level < flowConfigure.GetAcceleration().multigridSolver.level; level++)
	{
		// 依据网格层级编号level，获取相应层级网格，并以此网格定义物理场包，同时将物理场包置于物理场包容器中
		flowPackageVector.push_back(new Package::FlowPackage(level, subMesh, flowConfigure));
	}

	// 获取非定常标识
	unsteady = flowPackageVector[0]->GetUnsteadyStatus().unsteadyFlag;
	dualTime = flowPackageVector[0]->GetUnsteadyStatus().dualTime;

	fullMultigirdFlag = true;
	if (unsteady
		|| flowConfigure.GetControl().initialization.type == Initialization::Type::RESTART
		|| flowConfigure.GetControl().initialization.fullMultigridSteps <= 0)
	{
		fullMultigirdFlag = false;
	}

	// 获取初始化类型
	initialType = flowConfigure.GetControl().initialization.type;

	// 根据输入参数，确定多重网格总层数
	multigridLevel = flowConfigure.GetAcceleration().multigridSolver.level;

	// 依据输入参数中的时间格式，创建当前网格层级上物理场包更新的时间推进对象，并将其置于时间推进对象容器中        
	for (int level = 0; level < multigridLevel; level++)
		timeSchemeVector.push_back(new Time::Flow::FlowTimeManager(*flowPackageVector[level]));

	// 创建残值容器
	resultProcess = new FlowResultsProcess(subMesh, flowPackageVector);

	// 创建全多重求解器
	fullMultigird = nullptr;
	if (fullMultigirdFlag) fullMultigird = new FullMultigird(subMesh, flowPackageVector, timeSchemeVector, resultProcess);

	// 创建内循环求解器
	innerLoop = new InnerLoop(subMesh, flowPackageVector, timeSchemeVector, resultProcess);
}

OuterLoop::~OuterLoop()
{
	if (resultProcess != nullptr) { delete resultProcess; resultProcess = nullptr; }

	for (int i = 0; i < timeSchemeVector.size(); i++)
	{
		if (timeSchemeVector[i] != nullptr)
		{
			delete timeSchemeVector[i];
			timeSchemeVector[i] = nullptr;
		}
	}

	if (fullMultigird != nullptr) { delete fullMultigird; fullMultigird = nullptr; }
	if (innerLoop != nullptr) { delete innerLoop; innerLoop = nullptr; }

	for (int i = 0; i < flowPackageVector.size(); i++)
	{
		if (flowPackageVector[i] != nullptr)
		{
			delete flowPackageVector[i];
			flowPackageVector[i] = nullptr;
		}
	}
}

void OuterLoop::Initialize()
{
	MPIBarrier();

	if (GetMPIRank() == 0) Print("\nInitialize flow fields ...");

	// 物理场初始化
	flowPackageVector[0]->InitializeField(initialType);

	// 时间格式初始化
	timeSchemeVector[0]->Initialize(initialType);

	// 残差监控初始化
	resultProcess->Initialize(0);

	// 保存初始物理场
	const int innerInterval = flowConfigure.GetControl().innerLoop.interval;
	if (innerInterval > 0) resultProcess->SaveIntervalResults();
}

void OuterLoop::CloseMultigrid()
{
    innerLoop->CloseMultigrid();
}

void OuterLoop::Solve()
{
	MPIBarrier();

	// 全多重初始化流场
	if (fullMultigirdFlag)
	{
		if (GetMPIRank() == 0) Print("\n  Initialize with full multigird ...");
		fullMultigird->Solve();
	}

	// 获取外循环流场输出间隔及总迭代步数
	const int &outerInterval = flowConfigure.GetControl().outerLoop.interval;
	const int &outerLoopSteps = flowConfigure.GetControl().outerLoop.steps;
	const int &averagedStep = flowConfigure.GetControl().outerLoop.averagedStep;
	int computeAveragedStep = 0;

	// 非定常计算总物理时间及当前物理时间
	const Scalar &totalTime = flowPackageVector[0]->GetUnsteadyStatus().totalTime;
	const Scalar &currentTime = flowPackageVector[0]->GetUnsteadyStatus().currentTime;

	if (GetMPIRank() == 0) Print("\nBegin to solve ...");

	// 非定常计算,流场迭代求解外循环（物理时间迭代）
	for (int outerStep = 0; outerStep < outerLoopSteps; outerStep++)
	{
		// 非定常计算时保存上一时间步物理场
		if(dualTime)
		{
			resultProcess->Initialize(0);
			this->SetTimeStep();
			this->UpdateOldField(outerStep);
		}

		// 内迭代求解
		innerLoop->Solve();

		// 外循环物理场输出
		if (unsteady)
		{
			if (outerStep + 1 >= averagedStep)
			{
				resultProcess->CalculateMeanFields(computeAveragedStep);
				computeAveragedStep++;
			}
			
			resultProcess->MonitorPerStepOutLoop();
			CheckStatus(2401);

			if (currentTime >= totalTime || outerStep + 1 > outerLoopSteps)
			{
				resultProcess->SaveFinalResults();
				CheckStatus(2402);
				break;
			}
			else if (outerInterval > 0 && (outerStep + 1) % outerInterval == 0)
			{
				resultProcess->SaveIntervalResults();
				CheckStatus(2403);
			}
		}
	}
}

void OuterLoop::SetTimeStep()
{
	const Scalar &timeStep = flowPackageVector[0]->GetUnsteadyStatus().timeStep;
	for (int level = 0; level < flowPackageVector.size(); ++level)
		flowPackageVector[level]->UpdateCurrentTime(timeStep);
}

void OuterLoop::UpdateOldField(const int &outerStep)
{
	for (int level = 0; level < flowConfigure.GetAcceleration().multigridSolver.level; ++level)
	{
		const int unsteadyFieldSize = (int)flowPackageVector[0]->GetFieldUnsteadyField().size();
		for (int i = unsteadyFieldSize - 1; i > 0; --i)
		{
			const int j = i - 1;
			*flowPackageVector[level]->GetFieldUnsteadyField()[i].density = *flowPackageVector[level]->GetFieldUnsteadyField()[j].density;
			*flowPackageVector[level]->GetFieldUnsteadyField()[i].velocity = *flowPackageVector[level]->GetFieldUnsteadyField()[j].velocity;
			*flowPackageVector[level]->GetFieldUnsteadyField()[i].pressure = *flowPackageVector[level]->GetFieldUnsteadyField()[j].pressure;
			for (int k = 0; k < flowPackageVector[level]->GetTurbulentStatus().nVariable; ++k)
				*flowPackageVector[level]->GetFieldUnsteadyField()[i].turbulence[k] = *flowPackageVector[level]->GetFieldUnsteadyField()[j].turbulence[k];
		}
	}
}
